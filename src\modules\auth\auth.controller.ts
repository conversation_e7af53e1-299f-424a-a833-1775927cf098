import { Controller, Post, UseGuards, Get, Request, Body } from "@nestjs/common"
import { ApiTags, ApiOperation, ApiBearerAuth } from "@nestjs/swagger"
import { AuthService } from "./auth.service"
import { RegisterDto } from "./dto/register.dto"
import { LoginDto } from "./dto/login.dto"
import { FirebaseAuthGuard } from "../../common/guards/firebase-auth.guard"
import { UserRole } from "../../common/decorators/roles.decorator"

@ApiTags("Authentication")
@Controller("auth")
export class AuthController {
  constructor(private authService: AuthService) {}

  @Post("register")
  @ApiOperation({ summary: "Register a new user" })
  async register(@Body() registerDto: RegisterDto) {
    return this.authService.register(registerDto)
  }

  @Post("login")
  @ApiOperation({ summary: "Login user and get custom token" })
  async login(@Body() loginDto: LoginDto) {
    return this.authService.login(loginDto)
  }

  

  @Post("register-admin")
  @ApiOperation({ summary: "Register an admin user (for testing)" })
  async registerAdmin(@Body() registerDto: RegisterDto) {
    // Force the role to be admin
    const adminDto = { ...registerDto, role: UserRole.ADMIN }
    return this.authService.register(adminDto)
  }

  @Get('profile')
  @UseGuards(FirebaseAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user profile' })
  async getProfile(@Request() req: any) {
    const user = await this.authService.validateUser(req.user.uid);
    await this.authService.updateLastLogin(req.user.uid);
    return user;
  }
}
